"use client";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { updateCollapseLogging } from "@/modules/auth/redux/userSlice";
import { useQueryDrillhole } from "@/modules/drillhole/hooks/useQueryDrillhole";
import { clearDetail } from "@/modules/drillhole/redux/drillholeSlice";
import { getDetailDrillhole } from "@/modules/drillhole/redux/drillholeSlice/thunks";
import { useQueryGeologySuite } from "@/modules/geology-suite/hooks/useQueryGeologySuite";
import { getDetailGeologySuite } from "@/modules/geology-suite/redux/thunks";
import { useGetGeotechSuite } from "@/modules/geotech-suite/hooks/useGetGeotechSuite";
import { useQueryGeotechSuite } from "@/modules/geotech-suite/hooks/useQueryGeotechSuite";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import imageRequest from "@/modules/image/api/image.api";
import { IImageType } from "@/modules/image/interface/image.interface";
import { getDetailProject } from "@/modules/projects/redux/projectSlice/thunks";
import { getDetailStructure } from "@/modules/structure/redux/thunks";
import { Button, Image, Select, Tabs, Tag } from "antd";
import { isEmpty, isNaN, isNumber } from "lodash";
import { useRouter, useSearchParams } from "next/navigation";
import { Fragment, useEffect, useMemo, useState } from "react";
import { HiPlus } from "react-icons/hi";
import { toast } from "react-toastify";
import { DataEntryBody } from "../api/data-entry.api";
import { useGetListGeotechData } from "../hooks/useGetListGeotechData";
import { EnumLoggingExtraViews } from "../model/enum/logging.enum";
import {
  initLogging,
  setImageHyperRows,
  setImageRows,
  updateCurrentDepth,
  updateGeotechData,
  updateGeotechSelected,
  updateLoadingGeotechData,
  updateLoggingSuiteId,
  updateLoggingSuiteMode,
  updateLoggingSuiteSelected,
  updateMeasurePointsInterval,
  updatePreviewImageUrl,
  updateRefetchLoggingView,
  updateSegmentsDetailResult,
  updateSegmentsResult,
  updateSelectedDrilhole,
  updateSelectedSuite,
  updateSkipCount,
} from "../redux/loggingSlice";
import {
  getExtraViewModeImages,
  getGeologyData,
} from "../redux/loggingSlice/thunks";
import { OCRResultItem } from "../types/logging.types";
import { CalculationTab } from "./calculation/calculation-tab";
import { InfiniteScrollEntries } from "./InfiniteScrollEntries";
import { LoggingFromCreate } from "./logging-form-create";
import { LoggingGrid } from "./logging-grid";
import LoggingImage from "./logging-image";
import { LoggingNavigationImage } from "./logging-navigation-image";
import { LoggingVisualize } from "./logging-visualize";
import ModalEntryData from "./modal-entry-data";
import { ModalExport } from "./modal-export";
import ModalGeotechData from "./modal-geotech-data";
import { RenderGeotechData } from "./render-geotech-data";

export function Logging() {
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const searchParams = useSearchParams();
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const router = useRouter();
  const params = new URLSearchParams(queries);

  const imageTypeIdParam = params.get("imageTypeId");
  const imageSubtypeIdParam = params.get("imageSubtypeId");

  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const [directOCRdata, setDirectOCRdata] = useState<OCRResultItem[]>([]);
  const [directOCRdataRaw, setDirectOCRdataRaw] = useState<OCRResultItem[]>([]);
  const [viewModes, setViewModes] = useState<string[]>([]);
  const [isDrillholeLoading, setIsDrillholeLoading] = useState<boolean>(false);

  // Image type and subtype state management
  const [selectedImageType, setSelectedImageType] = useState<
    number | undefined
  >();
  const [selectedImageSubtype, setSelectedImageSubtype] = useState<
    number | undefined
  >();
  const [availableSubtypes, setAvailableSubtypes] = useState<any[]>([]);
  const [allImageTypes, setAllImageTypes] = useState<IImageType[]>([]);

  // Fetch image types
  const {
    data: imageTypeData,
    isLoading: isFetchingImageType,
    handleScroll: handleScrollImageType,
    searchParams: searchParamsImageType,
    setSearchParams: setSearchParamsImageType,
    setEnable: setEnableImageType,
    enable: enableImageType,
  } = useQueryImageType();
  useEffect(() => {
    setSearchParamsImageType({
      ...searchParamsImageType,
      projectId: globalProjectId,
      prospectId: globalProspectId,
    });
  }, [globalProjectId, globalProspectId, enableImageType]);
  useEffect(() => {
    setEnableImageType(true);
  }, []);

  const {
    previewImage,
    selectedDrillHole: selectedDrillhole,
    imageRows,
    toggleUpdateOCR,
    skipCount,
    loadingGeotechData: isLoadingGeotechData,
    loggingSuiteId,
    openModalGeotechData,
    refetchGeotechData,

    extraImageTypeId,
    extraImageSubtypeId,
  } = useAppSelector((state) => state?.logging);

  const dispatch = useAppDispatch();
  const projectId = useAppSelector((state) => state.user.userInfo?.projectId);
  const projectDetail = useAppSelector((state) => state.project.detail);
  const {
    data: _geologySuites,
    setEnable: setEnableGeologySuite,
    setSearchParams: setSearchParamsGeologySuite,
    setIsEnd: setIsEndGeologySuite,
  } = useQueryGeologySuite();

  useEffect(() => {
    setIsEndGeologySuite(false);
    setSearchParamsGeologySuite({
      projectId: globalProjectId,
    });
  }, [globalProjectId]);

  const [image, setImage] = useState<any>();
  const [totalImage, setTotalImage] = useState<any>();

  const collapseLogging = useAppSelector((state) => state.user.collapseLogging);

  // Init logging info
  const initLoggingInfo = (croppedImagesRow: any) => {
    const minDepthFrom = croppedImagesRow?.[0]?.depthFrom;
    const maxDepthTo = croppedImagesRow?.[croppedImagesRow.length - 1]?.depthTo;

    dispatch(initLogging(minDepthFrom));
    dispatch(
      updateCurrentDepth({
        depthFrom: minDepthFrom,
        depthTo: maxDepthTo,
      })
    );
  };
  const [currentImage, setCurrentImage] = useState(1);

  const queryFilter = useMemo(
    () => ({
      projectIds: globalProjectId ? [globalProjectId] : [],
      prospectIds: globalProspectId ? [globalProspectId] : [],
      holeIds: selectedDrillhole?.value
        ? [Number(selectedDrillhole?.value)]
        : [],
      maxResultCount: 1,
      skipCount: currentImage - 1,
      imageTypeId: selectedImageType,
      imageSubtypeId: selectedImageSubtype,
    }),
    [
      selectedDrillhole,
      globalProjectId,
      globalProspectId,
      selectedImageType,
      selectedImageSubtype,
    ]
  );

  const getCurrentImage = async (
    drillholeId?: number,
    index?: number,
    imageTypeId?: number,
    imageSubtypeId?: number,
    imageCategory?: number
  ) => {
    if (!drillholeId) return;
    const imageDetail = await imageRequest.getImages({
      ...queryFilter,
      holeIds: [Number(drillholeId)],
      skipCount: (index ?? 1) - 1,
      imageTypeId: imageTypeId,
      imageSubtypeId: imageSubtypeId,
      imageCategory: imageCategory !== 0 ? imageCategory : undefined,
    });

    const res = imageDetail?.data;

    setImage(res?.items?.[0]);
    setTotalImage(res?.pagination?.total);

    const _croppedImages = res?.items[0]?.croppedImages ?? [];
    let croppedImagesRow: any[] = [];

    croppedImagesRow = _croppedImages
      ?.map((item: any) => {
        return {
          ...item,
          coordinate: JSON.parse(item.coordinate),
        };
      })
      .filter((item: any) => item.type?.toLowerCase() === "row");

    dispatch(setImageRows(croppedImagesRow));

    initLoggingInfo(croppedImagesRow);

    // Update segmentations result
    const segmentations = JSON.parse(res?.items[0]?.segmentResult ?? "[]");
    const segmentationsDetail = JSON.parse(
      res?.items[0]?.segmentDetailResult ?? "[]"
    );

    dispatch(
      updateSegmentsResult(
        segmentations.map((segmentation) => ({
          ...segmentation,
          rowIndex: segmentation.rowIndex + 1,
        }))
      )
    );

    dispatch(
      updateSegmentsDetailResult(
        segmentationsDetail.map((segmentation) => ({
          ...segmentation,
          rowIndex: segmentation.rowIndex + 1,
        }))
      )
    );

    if (
      viewModes.includes(EnumLoggingExtraViews.Below) ||
      viewModes.includes(EnumLoggingExtraViews.Overlay)
    ) {
      dispatch(setImageHyperRows([]));
      dispatch(
        getExtraViewModeImages({
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
          holeIds: [drillholeId],
          depthFrom: res?.items[0]?.depthFrom,
          depthTo: res?.items[0]?.depthTo,
          imageTypeId: extraImageTypeId,
          imageSubtypeId: extraImageSubtypeId,
        })
      );
    } else {
      dispatch(setImageHyperRows([]));
    }

    //GET DIRECT OCR
    const dataRaw = res?.items[0]?.ocrResult ?? "[]";
    const dataRawParse: OCRResultItem[] = JSON.parse(dataRaw).filter((data) => {
      return !isNaN(Number(data.text));
    });
    if (!dataRawParse.length) {
      return setDirectOCRdata([]);
    }

    setDirectOCRdata(dataRawParse);

    setDirectOCRdataRaw(dataRawParse);
    ``;
  };
  const loggingSuiteIdParams = params.get("loggingSuiteId");
  const handlePreviewVisibleChange = (visible: any) => {
    if (!visible) {
      dispatch(
        updatePreviewImageUrl({
          visible: false,
          url: "",
        })
      );
    }
  };

  useEffect(() => {
    if (!image?.id) return;
    imageRequest
      .updateResultOCR({
        id: image?.id,
        ocr: JSON.stringify(directOCRdata),
      })
      .then(() => {})
      .catch((err) => {
        toast.error("Update OCR failed");
      });
  }, [toggleUpdateOCR]);

  useEffect(() => {
    if (projectId) {
      dispatch(getDetailProject(projectId.toString()));
    }
  }, [projectId]);

  // Populate allImageTypes when imageTypeData is loaded
  useEffect(() => {
    if (imageTypeData?.data?.items) {
      setAllImageTypes(imageTypeData.data.items);
    }
  }, [imageTypeData]);

  const [valueDepth, setValueDepth] = useState<{
    depthFrom: number | null;
    depthTo: number | null;
  }>({
    depthFrom: null,
    depthTo: null,
  });

  const refetchDataEntry = async (isSaveLine = true, drillholeId?: number) => {
    const drillHoleId = drillholeId ?? Number(selectedDrillhole?.value);
    await dispatch(
      getGeologyData({
        geology: {
          GeologySuiteId: Number(savedSuites),
          DrillholeId: drillHoleId,
        },
        image: queryFilter,
      })
    ).unwrap();
    // isSaveLine && dispatch(saveLoggingLine());
  };

  const [modalState, setModalState] = useState<{
    isOpen: boolean;
    detail?: DataEntryBody;
    type: "create" | "update" | "delete" | "view";
  }>({
    isOpen: false,
    detail: undefined,
    type: "create",
  });
  const handleOpenCreateEntry = () => {
    setModalState({
      isOpen: true,
      detail: undefined,
      type: "create",
    });
  };

  useEffect(() => {
    if (!globalProjectId || !globalProspectId || !selectedDrillhole?.value) {
      dispatch(clearDetail());
      return;
    }
  }, [globalProjectId, globalProspectId, selectedDrillhole?.value]);

  // Enhanced subtype selection function for standard image types
  const selectDefaultSubtype = async (imageTypeId: number, subtypes: any[]) => {
    if (!selectedDrillhole?.value || !subtypes.length) return;
    let selectedSubtypeId: number | undefined;

    if (imageSubtypeIdParam) {
      selectedSubtypeId = Number(imageSubtypeIdParam);
    } else {
      // Find wet subtype (isWet === true)
      const wetSubtype = subtypes.find((subtype) => subtype.isWet === true);

      // Find dry subtype (isDry === true)
      const drySubtype = subtypes.find((subtype) => subtype.isDry === true);

      // Test wet subtype first if available
      if (wetSubtype) {
        try {
          const wetImageResponse = await imageRequest.getImages({
            ...queryFilter,
            holeIds: [Number(selectedDrillhole?.value)],
            skipCount: 0,
            maxResultCount: 1,
            imageTypeId: imageTypeId,
            imageSubtypeId: Number(wetSubtype.id),
          });

          // If wet subtype has images, use it
          if (wetImageResponse?.data?.items?.length > 0) {
            selectedSubtypeId = Number(wetSubtype.id);
          }
        } catch (error) {}
      }

      // If wet subtype is not available or has no images, test dry subtype
      if (!selectedSubtypeId && drySubtype) {
        try {
          const dryImageResponse = await imageRequest.getImages({
            ...queryFilter,
            holeIds: [Number(selectedDrillhole?.value)],
            skipCount: 0,
            maxResultCount: 1,
            imageTypeId: imageTypeId,
            imageSubtypeId: Number(drySubtype.id),
          });

          // If dry subtype has images, use it
          if (dryImageResponse?.data?.items?.length > 0) {
            selectedSubtypeId = Number(drySubtype.id);
          }
        } catch (error) {}
      }
      if (!selectedSubtypeId && wetSubtype) {
        selectedSubtypeId = Number(wetSubtype.id);
      }
    }

    // Set the selected subtype and trigger image loading

    if (selectedSubtypeId) {
      setSelectedImageSubtype(selectedSubtypeId);

      // Trigger image loading with the selected subtype
      const imageSkipCount = skipCount ?? 0;
      getCurrentImage(
        selectedDrillhole?.value,
        imageSkipCount + 1,
        imageTypeId,
        selectedSubtypeId,
        undefined
      );
    } else {
      // No suitable subtype found, load images without subtype filter
      const imageSkipCount = skipCount ?? 0;
      getCurrentImage(
        selectedDrillhole?.value,
        imageSkipCount + 1,
        imageTypeId,
        undefined,
        undefined
      );
    }
  };

  // Enhanced default selection logic for Image Types & Subtypes
  useEffect(() => {
    // Ensure we have the necessary data
    if (!allImageTypes || allImageTypes.length === 0 || selectedImageType) {
      return;
    }

    let defaultImageTypeId: number | null = null;

    if (imageTypeIdParam) {
      defaultImageTypeId = Number(imageTypeIdParam);
    } else {
      if (projectDetail?.imageType?.id) {
        const defaultTypeId = Number(projectDetail?.imageType?.id);

        // Verify this image type exists in allImageTypes
        const foundImageType = allImageTypes.find(
          (type) => type.id === defaultTypeId
        );
        if (foundImageType) {
          defaultImageTypeId = defaultTypeId;
        }
      }

      // Fallback default: Find first standard image type
      if (!defaultImageTypeId) {
        const standardImageType = allImageTypes.find(
          (type) => type.isStandard === true
        );
        if (standardImageType) {
          defaultImageTypeId = standardImageType.id;
        }
      }
    }

    // Apply default selection if we found a valid image type
    if (defaultImageTypeId) {
      const defaultType = allImageTypes.find(
        (type) => type.id === defaultImageTypeId
      );

      if (defaultType) {
        setSelectedImageType(defaultImageTypeId);
        setAvailableSubtypes(defaultType.imageSubtypes || []);

        // Find isWet subtype
        const wetSubtype = defaultType.imageSubtypes.find(
          (subtype) => subtype.isWet === true
        );

        wetSubtype?.id && setSelectedImageSubtype(Number(wetSubtype.id));

        // Enhanced subtype selection logic for standard image types
        if (defaultType.imageSubtypes?.length > 0) {
          selectDefaultSubtype(defaultImageTypeId, defaultType.imageSubtypes);
        } else {
          // For non-standard types, just trigger image loading without subtype
          if (selectedDrillhole?.value) {
            const imageSkipCount = skipCount ?? 0;
            getCurrentImage(
              selectedDrillhole?.value,
              imageSkipCount + 1,
              defaultImageTypeId,
              undefined,
              undefined
            );
          }
        }
      }
    }
  }, [
    projectDetail?.imageType?.id,
    allImageTypes,
    selectedImageType,
    selectedDrillhole?.value,
    skipCount,
    imageTypeIdParam,
    imageSubtypeIdParam,
  ]);

  // Update the handleDrillholeChange function
  const handleDrillholeChange = async (drillHoleId: string, option: any) => {
    if (!drillHoleId) return;

    setIsDrillholeLoading(true); // Set loading state to true
    dispatch(
      updateSelectedDrilhole({
        value: option?.value,
        label: option.label,
      })
    );
    params.set("drillholeId", drillHoleId);
    router.push(`${window.location.pathname}?${params.toString()}`);

    if (activeKey === "Image") {
      setViewModes([]);
      dispatch(setImageHyperRows([]));
      await refetchDataEntry(true, Number(drillHoleId));

      const promises: any[] = [];
      promises.push(
        getCurrentImage(
          Number(drillHoleId),
          1,
          selectedImageType,
          selectedImageSubtype
        )
      );

      await Promise.all(promises).finally(() => {
        setCurrentImage(1);
      });
    }

    if (activeKey === "Grid") {
      await refetchDataEntry(false, Number(drillHoleId));
    }

    if (activeKey === "Visualize" || activeKey === "Image") {
      dispatch(updateRefetchLoggingView());
    }
    setIsDrillholeLoading(false); // Reset loading state when all operations are done
  };

  const handleImageChange = (index: number) => {
    if (!selectedDrillhole?.value) return;
    getCurrentImage(
      selectedDrillhole.value,
      index,
      selectedImageType,
      selectedImageSubtype
    );
    setCurrentImage(index);

    // Save skipCount to redux
    dispatch(updateSkipCount(index - 1));
  };

  const handleGeologySuiteChange = (value: string) => {
    dispatch(updateSelectedSuite(value));
    dispatch(updateLoggingSuiteMode("Geology"));

    if (!selectedDrillhole?.value) return;
    dispatch(
      getGeologyData({
        geology: {
          GeologySuiteId: Number(value),
          DrillholeId: Number(selectedDrillhole?.value),
        },
        image: queryFilter,
      })
    );
  };

  const { data: geotechSuiteDetail, request: requestGetGeotechSuiteDetail } =
    useGetGeotechSuite();

  const handleGeoTechChange = (value: string) => {
    dispatch(updateLoggingSuiteMode("Geotech"));
    dispatch(updateLoggingSuiteId(Number(value)));
    requestGetGeotechSuiteDetail(value);
  };

  const {
    data: _geotechSuites,
    setEnable: setEnableGeotechSuite,
    setSearchParams: setSearchParamsGeotechSuite,
    setIsEnd: setIsEndGeotechSuite,
  } = useQueryGeotechSuite();
  useEffect(() => {
    setEnableGeotechSuite(true);
  }, []);
  useEffect(() => {
    setIsEndGeotechSuite(false);
    setSearchParamsGeotechSuite({
      projectId: globalProjectId,
    });
  }, [globalProjectId]);
  const { request: requestGetListGeotechData } = useGetListGeotechData();
  const drillholeId = useAppSelector(
    (state) => state.logging?.selectedDrillHole?.value
  );
  const structureId = useAppSelector((state) => state.structure?.detail?.id);

  const refetchGeotechDataHandler = ({
    drillholeId,
    structureId,
    loggingSuiteId,
  }: {
    drillholeId: number;
    structureId: number;
    loggingSuiteId: number;
  }) => {
    requestGetListGeotechData(
      {
        DrillHoleId: Number(drillholeId),
        GeotechFieldId: Number(structureId),
        GeotechSuiteId: Number(loggingSuiteId),
        maxResultCount: 10000,
        skipCount: 0,
      },
      (res) => {
        dispatch(updateGeotechData(res?.items));
      },
      () => {
        dispatch(updateGeotechData([]));
      }
    );
  };

  const geotechSuiteIdParams = params.get("geotechSuiteId");
  useEffect(() => {
    if (geotechSuiteIdParams) {
      handleSelectGeotechSuite(geotechSuiteIdParams);
    }
  }, [geotechSuiteIdParams]);
  const handleSelectGeotechSuite = (value: string) => {
    if (!value) {
      params.delete("geotechSuiteId");
      router.push(`${window.location.pathname}?${params.toString()}`);
      return;
    }
    params.set("geotechSuiteId", value);
    router.push(`${window.location.pathname}?${params.toString()}`);
    dispatch(updateGeotechSelected(value));
    if (!isLoadingGeotechData) {
      dispatch(getDetailStructure(value));
      dispatch(updateLoadingGeotechData(true));
      setTimeout(() => {
        dispatch(updateLoadingGeotechData(false));
      }, 1000);
      dispatch(updateMeasurePointsInterval({}));
    }
  };
  useEffect(() => {
    if (drillholeId && structureId && loggingSuiteId) {
      refetchGeotechDataHandler({
        drillholeId: Number(drillholeId),
        structureId,
        loggingSuiteId,
      });
    }
  }, [drillholeId, structureId, loggingSuiteId, refetchGeotechData]);

  const activeKey = params.get("activeKey") ?? "Image";
  const handleChangeActiveKey = (value: string) => {
    params.set("activeKey", value);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  useEffect(() => {
    dispatch(updateCollapseLogging(true));
  }, []);

  const [modalExport, setModalExport] = useState(false);

  // Image type change handler
  const handleImageTypeChange = (value: number) => {
    setSelectedImageType(value);
    setSelectedImageSubtype(undefined); // Reset subtype when type changes

    // Find the selected image type and update available subtypes
    const selectedType = allImageTypes.find((type) => type.id === value);
    if (selectedType) {
      setAvailableSubtypes(selectedType.imageSubtypes || []);
    } else {
      setAvailableSubtypes([]);
    }

    // Update URL params
    if (value) {
      params.set("imageTypeId", value.toString());
      params.delete("imageSubtypeId");
    } else {
      params.delete("imageTypeId");
    }

    router.push(`${window.location.pathname}?${params.toString()}`);

    // Refresh image data with new type
    if (selectedDrillhole?.value) {
      const imageSkipCount = skipCount ?? 0;
      getCurrentImage(
        selectedDrillhole.value,
        imageSkipCount + 1,
        undefined,
        value,
        undefined
      );
      setCurrentImage(imageSkipCount + 1);
    }
  };

  // Image subtype change handler
  const handleImageSubtypeChange = (value: number) => {
    setSelectedImageSubtype(value);

    // Update URL params
    if (value) {
      params.set("imageSubtypeId", value.toString());
    } else {
      params.delete("imageSubtypeId");
    }
    router.push(`${window.location.pathname}?${params.toString()}`);

    // Refresh image data with new subtype
    if (selectedDrillhole?.value) {
      const imageSkipCount = skipCount ?? 0;
      getCurrentImage(
        selectedDrillhole.value,
        imageSkipCount + 1,
        selectedImageType,
        value,
        undefined
      );
      setCurrentImage(imageSkipCount + 1);
    }
  };
  const handleOnChangeLoggingSuite = (value: string) => {
    if (!value) {
      params.delete("loggingSuiteId");
      router.push(`${window.location.pathname}?${params.toString()}`);
      return;
    }
    params.set("loggingSuiteId", value);
    params.delete("geotechSuiteId");
    dispatch(updateGeotechData([]));
    router.push(`${window.location.pathname}?${params.toString()}`);
    dispatch(updateLoggingSuiteSelected(value));
    const suiteId = value.split("-")[1];
    if (value.startsWith("geologySuites")) {
      handleGeologySuiteChange(suiteId);
    } else {
      handleGeoTechChange(suiteId);
    }
  };
  const optionsGeotechSuite = useMemo(() => {
    return geotechSuiteDetail?.structures?.map((geotechSuite) => ({
      label: geotechSuite.name,
      value: geotechSuite.id.toString(),
    }));
  }, [geotechSuiteDetail]);
  const savedSuites = loggingSuiteIdParams?.startsWith("geologySuites")
    ? loggingSuiteIdParams?.split("-")[1]
    : null;

  useEffect(() => {
    savedSuites && dispatch(getDetailGeologySuite(savedSuites));
  }, [savedSuites]);

  // Query hooks
  const {
    setEnable: setEnableDrillhole,
    data: drillholeData,
    searchParams: searchParamsDrillhole,
    setSearchParams: setSearchParamsDrillhole,
  } = useQueryDrillhole();
  useEffect(() => {
    if (globalProjectId && globalProspectId) {
      setEnableDrillhole(true);
      setSearchParamsDrillhole((prev) => {
        const newParams = {
          ...prev,
          projectIds: [globalProjectId],
          prospectIds: [globalProspectId],
        };
        return newParams;
      });
    }
  }, [globalProjectId, globalProspectId]);

  // Get detail drillhole for display max depth on the header top
  useEffect(() => {
    if (selectedDrillhole?.value) {
      dispatch(getDetailDrillhole(Number(selectedDrillhole?.value)));
    }
  }, [selectedDrillhole?.value]);

  return (
    <>
      {openModalGeotechData && <ModalGeotechData />}
      {modalExport && (
        <ModalExport modalState={modalExport} setModalState={setModalExport} />
      )}

      <ModalEntryData
        modalState={modalState}
        setModalState={setModalState}
        refetchDataEntry={refetchDataEntry}
      />
      {previewImage?.visible && (
        <Image
          src={previewImage?.url}
          preview={{
            visible: !!previewImage?.visible,
            onVisibleChange: handlePreviewVisibleChange,
          }}
          style={{
            display: "none",
          }}
        />
      )}

      <div className="flex gap-2 h-full relative">
        {/* Loading overlay when drillhole data is loading */}
        {isDrillholeLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-70 z-50 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <div
                className="w-12 h-12 border-4 rounded-full animate-spin mb-4"
                style={{
                  borderColor: "#1890ff",
                  borderTopColor: "transparent",
                }}
              ></div>
              <p className="text-lg font-medium" style={{ color: "#1890ff" }}>
                Loading drillhole data...
              </p>
            </div>
          </div>
        )}
        <div className=" h-full max-h-[calc(100vh_-_106px)] relative">
          {collapseLogging && (
            <div className="w-64 flex flex-col gap-2 pr-2 border-r border-gray-300 h-full ">
              <div className="flex items-center justify-between text-xl">
                <div className="font-bold">Logging</div>
                {isNumber(image?.depthFrom) &&
                  isNumber(image?.depthTo) &&
                  !isEmpty(selectedDrillhole?.label) && (
                    <div className="flex items-center gap-1">
                      <Tag color="blue">{image?.depthFrom}</Tag>
                      <p>-</p>
                      <Tag color="gold">{image?.depthTo}</Tag>
                    </div>
                  )}
              </div>
              {projectDetail?.coreTrayLength && (
                <div>Core tray length: {projectDetail?.coreTrayLength}m</div>
              )}
              <Select
                className="w-full"
                disabled={!globalProjectId || !globalProspectId}
                showSearch
                filterOption={false}
                options={
                  drillholeData?.data?.items?.map((drillhole: any) => ({
                    label: drillhole.name,
                    value: drillhole.id?.toString(),
                  })) ?? []
                }
                onSearch={(value) =>
                  setSearchParamsDrillhole({
                    ...searchParamsDrillhole,
                    keyword: value,
                  })
                }
                onBlur={() =>
                  setSearchParamsDrillhole({
                    ...searchParamsDrillhole,
                    keyword: undefined,
                  })
                }
                searchValue={searchParamsDrillhole?.keyword}
                value={selectedDrillhole?.value?.toString()}
                placeholder="Select drillhole"
                onChange={handleDrillholeChange}
              />
              <div className="flex flex-col gap-2">
                <span className="text-sm font-bold">Image Type</span>
                <Select
                  className="w-full"
                  placeholder="Select image type"
                  value={selectedImageType}
                  onChange={handleImageTypeChange}
                  loading={isFetchingImageType}
                  options={imageTypeData?.data?.items?.map((type: any) => ({
                    label: type.name,
                    value: type.id,
                  }))}
                  allowClear
                  onSearch={(value) => {
                    setSearchParamsImageType({
                      ...searchParamsImageType,
                      keyword: value,
                    });
                  }}
                  onBlur={() => {
                    setSearchParamsImageType({
                      ...searchParamsImageType,
                      keyword: undefined,
                    });
                  }}
                  searchValue={searchParamsImageType?.keyword}
                  onPopupScroll={(event: any) => {
                    handleScrollImageType(event);
                  }}
                  filterOption={false}
                  showSearch
                />
              </div>
              <div className="flex flex-col gap-2">
                <span className="text-sm font-bold">Image Subtype</span>
                <Select
                  className="w-full"
                  placeholder="Select image subtype"
                  value={selectedImageSubtype}
                  onChange={handleImageSubtypeChange}
                  disabled={
                    !selectedImageType || availableSubtypes.length === 0
                  }
                  options={availableSubtypes.map((subtype) => ({
                    label: subtype.name,
                    value: subtype.id,
                  }))}
                  allowClear
                  notFoundContent={
                    !selectedImageType
                      ? "Please select an image type first"
                      : "No subtypes available"
                  }
                />
              </div>
              <div className="flex gap-2 flex-col">
                <p className="font-bold">Logging Suite</p>
                <Select
                  allowClear
                  value={loggingSuiteIdParams}
                  disabled={!globalProjectId}
                  key={loggingSuiteIdParams}
                  placeholder="Choose suite"
                  options={(_geologySuites?.data?.items ?? [])
                    .map((attribute) => ({
                      label: attribute.name,
                      value: `geologySuites-${attribute.id}`,
                    }))
                    .concat(
                      (_geotechSuites?.data?.items ?? []).map(
                        (geotechSuite) => ({
                          label: geotechSuite.name,
                          value: `geotechSuites-${geotechSuite.id}`,
                        })
                      )
                    )}
                  onChange={handleOnChangeLoggingSuite}
                />
              </div>

              {loggingSuiteIdParams?.startsWith("geotechSuites") && (
                <>
                  <p className="font-bold">Structure</p>
                  <Select
                    key={JSON.stringify(geotechSuiteIdParams)}
                    value={geotechSuiteIdParams}
                    placeholder="Select structure"
                    options={optionsGeotechSuite}
                    allowClear
                    filterOption={false}
                    onChange={handleSelectGeotechSuite}
                  />
                </>
              )}

              {!isEmpty(selectedDrillhole?.label) && loggingSuiteIdParams && (
                <Fragment>
                  <div className="flex flex-col gap-1 mb-2 bg-white p-2 border rounded-lg">
                    <LoggingFromCreate
                      filter={queryFilter}
                      valueDepth={valueDepth}
                      setValueDepth={setValueDepth}
                      modalState={modalState}
                    />
                    <Button
                      type="primary"
                      icon={<HiPlus />}
                      onClick={handleOpenCreateEntry}
                    >
                      Add Entry
                    </Button>
                  </div>
                  {loggingSuiteIdParams?.startsWith("geotechSuites") ? (
                    <div className="max-h-full overflow-y-auto overflow-x-hidden scrollbar-thin bg-white rounded-lg border gap-2">
                      <div className="relative h-8 flex justify-center items-center">
                        <div className="flex items-center">
                          <span className="mr-1">Entries</span>
                        </div>
                      </div>
                      <div className="flex flex-col gap-2 p-2">
                        <RenderGeotechData />
                      </div>
                    </div>
                  ) : (
                    <InfiniteScrollEntries
                      geologySuiteId={
                        savedSuites ? Number(savedSuites) : undefined
                      }
                      drillholeId={
                        selectedDrillhole?.value
                          ? Number(selectedDrillhole.value)
                          : undefined
                      }
                      setModalState={setModalState}
                    />
                  )}
                </Fragment>
              )}
            </div>
          )}
        </div>
        {/* Empty state for no drill hole selected */}
        {isEmpty(selectedDrillhole?.label) && (
          <div className="flex flex-col items-center justify-center w-full">
            <div className="flex flex-col items-center justify-center max-w-md p-8 bg-white rounded-lg shadow-md">
              <img
                src="/images/empty-box.png"
                alt="No drill hole selected"
                className="w-40 h-40 mb-6 opacity-80"
              />
              <h2 className="text-2xl font-bold text-gray-700 mb-2">
                No drill hole selected
              </h2>
              <p className="text-gray-500 text-center mb-6">
                Please select a drill hole from the dropdown menu to view the
                logging data.
              </p>
              <div className="flex items-center justify-center">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-ping mr-2"></div>
                <span className="text-blue-500 font-medium">
                  Select a drill hole to get started
                </span>
              </div>
            </div>
          </div>
        )}

        <div className="flex-1 h-full">
          {selectedDrillhole?.value && activeKey === "Image" && (
            <div className="lg:absolute top-0 right-0 z-10">
              <LoggingNavigationImage
                total={totalImage}
                currentImage={currentImage}
                setCurrentImage={setCurrentImage}
                onGoToPage={handleImageChange}
              />
            </div>
          )}

          {selectedDrillhole?.value && (
            <Tabs
              activeKey={activeKey}
              onChange={handleChangeActiveKey}
              className="h-full"
              destroyInactiveTabPane
              items={[
                {
                  key: "Image",
                  label: "Image",
                  children: (
                    <LoggingImage
                      imageRows={imageRows}
                      viewModes={viewModes}
                      setViewModes={setViewModes}
                      directOCRdata={directOCRdata}
                      setDirectOCRdata={setDirectOCRdata}
                      image={image}
                      directOCRdataRaw={directOCRdataRaw}
                      getCurrentImage={getCurrentImage}
                      setCurrentImage={setCurrentImage}
                      selectedImageType={selectedImageType}
                      selectedImageSubtype={selectedImageSubtype}
                    />
                  ),
                },
                {
                  key: "Grid",
                  label: "Grid",
                  className: "h-full",
                  children: <LoggingGrid refetchDataEntry={refetchDataEntry} />,
                },
                {
                  key: "calculation",
                  label: "Calculations",
                  children: <CalculationTab setModalExport={setModalExport} />,
                },
                {
                  key: "Visualize",
                  label: "Visualize",
                  className: "h-full",
                  children: <LoggingVisualize />,
                },
              ]}
              type="line"
            />
          )}
        </div>
      </div>
    </>
  );
}
